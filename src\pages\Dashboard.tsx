import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import {
  BookOpen,
  GraduationCap,
  Clock,
  Award,
  TrendingUp,
  Users,
  Monitor
} from "lucide-react";
import doctorAvatar from "@/assets/doctor-avatar-female.jpg";
import LearningAnalyticsCharts from "@/components/LearningAnalyticsCharts";

const Dashboard = () => {
  const userProfile = {
    name: "Dr. <PERSON>",
    role: "Senior Nurse",
    department: "Emergency Medicine",
    avatar: doctorAvatar,
  };

  const dashboardStats = [
    { label: "Nominated Trainings", value: "8", icon: BookOpen, color: "text-primary" },
    { label: "Completed Trainings", value: "5", icon: GraduationCap, color: "text-success" },
    { label: "Training Actualization", value: "63%", icon: TrendingUp, color: "text-primary" },
    { label: "Classroom Trainings", value: "4", icon: Users, color: "text-accent" },
    { label: "Online Trainings", value: "1", icon: Monitor, color: "text-primary" },
  ];

  const programsDue = [
    { name: "Medcare Look", daysLeft: 15, urgent: true },
    { name: "Basic Life Support", daysLeft: 20, urgent: false },
    { name: "Nursing Finishing School", daysLeft: 30, urgent: false },
  ];



  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-foreground">My Learning Dashboard</h1>
          <p className="text-muted-foreground">Welcome back to your learning journey</p>
        </div>
        <div className="flex items-center gap-4">
          <Avatar className="w-12 h-12 border-2 border-primary/20">
            <AvatarImage src={userProfile.avatar} alt={userProfile.name} />
            <AvatarFallback>SJ</AvatarFallback>
          </Avatar>
          <div className="text-right">
            <div className="font-semibold">{userProfile.name}</div>
            <div className="text-sm text-muted-foreground">{userProfile.role}</div>
          </div>
        </div>
      </div>

      {/* Dashboard Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-5 gap-4">
        {dashboardStats.map((stat, index) => (
          <Card key={index} className="shadow-card">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">{stat.label}</p>
                  <p className={`text-2xl font-bold ${stat.color}`}>{stat.value}</p>
                </div>
                <stat.icon className={`w-8 h-8 ${stat.color}`} />
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Programs Due in 90 Days */}
        <Card className="shadow-card">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Clock className="w-5 h-5 text-warning" />
              Programs due in 90 days
            </CardTitle>
            <CardDescription>
              Programs must be displayed in ascending order of days left to complete before expiry
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {programsDue.map((program, index) => (
              <div key={index} className="flex items-center justify-between p-4 bg-muted/30 rounded-lg">
                <div>
                  <div className="font-medium">{program.name}</div>
                  <div className="text-sm text-muted-foreground">{program.daysLeft} days remaining</div>
                </div>
                <Badge variant={program.urgent ? "destructive" : "secondary"}>
                  {program.daysLeft} days
                </Badge>
              </div>
            ))}
            <Button variant="outline" className="w-full mt-4">
              View All Programs
            </Button>
          </CardContent>
        </Card>

        {/* Recent Training Activities */}
        <Card className="shadow-card">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BookOpen className="w-5 h-5 text-primary" />
              Recent Training Activities
            </CardTitle>
            <CardDescription>
              Your latest training progress and achievements
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-3">
              <div className="flex items-center justify-between p-3 bg-success/10 rounded-lg border border-success/20">
                <div className="flex items-center gap-3">
                  <div className="w-8 h-8 bg-success/20 rounded-full flex items-center justify-center">
                    <GraduationCap className="w-4 h-4 text-success" />
                  </div>
                  <div>
                    <div className="font-medium text-sm">Emergency Response Protocol</div>
                    <div className="text-xs text-muted-foreground">Completed 2 days ago</div>
                  </div>
                </div>
                <Badge variant="secondary" className="bg-success/10 text-success border-success/20">
                  Completed
                </Badge>
              </div>

              <div className="flex items-center justify-between p-3 bg-primary/10 rounded-lg border border-primary/20">
                <div className="flex items-center gap-3">
                  <div className="w-8 h-8 bg-primary/20 rounded-full flex items-center justify-center">
                    <Clock className="w-4 h-4 text-primary" />
                  </div>
                  <div>
                    <div className="font-medium text-sm">Patient Safety Standards</div>
                    <div className="text-xs text-muted-foreground">In progress - 75% complete</div>
                  </div>
                </div>
                <div className="w-16">
                  <Progress value={75} className="h-2" />
                </div>
              </div>

              <div className="flex items-center justify-between p-3 bg-warning/10 rounded-lg border border-warning/20">
                <div className="flex items-center gap-3">
                  <div className="w-8 h-8 bg-warning/20 rounded-full flex items-center justify-center">
                    <Award className="w-4 h-4 text-warning" />
                  </div>
                  <div>
                    <div className="font-medium text-sm">Advanced Life Support</div>
                    <div className="text-xs text-muted-foreground">Starts in 3 days</div>
                  </div>
                </div>
                <Badge variant="outline" className="border-warning/50 text-warning">
                  Upcoming
                </Badge>
              </div>
            </div>

            <Button variant="outline" className="w-full mt-4">
              View All Activities
            </Button>
          </CardContent>
        </Card>
      </div>

      {/* Learning Analytics Charts */}
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-xl font-bold text-foreground">Learning Analytics Dashboard</h2>
            <p className="text-muted-foreground">Comprehensive analytics and insights</p>
          </div>
        </div>
        <LearningAnalyticsCharts />
      </div>

      
    </div>
  );
};

export default Dashboard;
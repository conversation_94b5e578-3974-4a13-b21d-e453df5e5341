@tailwind base;
@tailwind components;
@tailwind utilities;

/* Definition of the design system. All colors, gradients, fonts, etc should be defined here. 
All colors MUST be HSL.
*/

@layer base {
  :root {
    /* Healthcare Color Palette */
    --background: 210 25% 98%;
    --foreground: 220 15% 20%;

    --card: 0 0% 100%;
    --card-foreground: 220 15% 20%;

    --popover: 0 0% 100%;
    --popover-foreground: 220 15% 20%;

    /* Medical Blue Primary */
    --primary: 210 75% 50%;
    --primary-foreground: 0 0% 100%;
    --primary-light: 210 85% 65%;
    --primary-dark: 210 65% 35%;

    /* Professional Secondary */
    --secondary: 210 40% 95%;
    --secondary-foreground: 220 15% 25%;

    --muted: 210 30% 96%;
    --muted-foreground: 220 10% 45%;

    --accent: 195 85% 55%;
    --accent-foreground: 0 0% 100%;

    --destructive: 0 75% 60%;
    --destructive-foreground: 0 0% 100%;

    --border: 210 20% 88%;
    --input: 210 20% 92%;
    --ring: 210 75% 50%;

    /* Healthcare Gradients */
    --gradient-primary: linear-gradient(135deg, hsl(210 75% 50%), hsl(195 85% 55%));
    --gradient-card: linear-gradient(145deg, hsl(0 0% 100%), hsl(210 25% 98%));
    --gradient-hero: linear-gradient(135deg, hsl(210 75% 50%) 0%, hsl(195 85% 55%) 50%, hsl(210 65% 35%) 100%);
    
    /* Professional Shadows */
    --shadow-sm: 0 2px 4px hsla(210, 25%, 10%, 0.08);
    --shadow-md: 0 4px 12px hsla(210, 25%, 10%, 0.12);
    --shadow-lg: 0 8px 24px hsla(210, 25%, 10%, 0.15);
    --shadow-card: 0 2px 8px hsla(210, 30%, 8%, 0.08);

    /* Healthcare Status Colors */
    --success: 145 65% 50%;
    --success-foreground: 0 0% 100%;
    --warning: 45 90% 55%;
    --warning-foreground: 45 15% 15%;
    --info: 210 75% 50%;
    --info-foreground: 0 0% 100%;

    --radius: 0.75rem;

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    /* Dark Healthcare Color Palette */
    --background: 220 25% 8%;
    --foreground: 210 25% 95%;

    --card: 220 20% 12%;
    --card-foreground: 210 25% 95%;

    --popover: 220 20% 12%;
    --popover-foreground: 210 25% 95%;

    /* Dark Medical Blue Primary */
    --primary: 210 85% 65%;
    --primary-foreground: 220 25% 8%;
    --primary-light: 210 90% 75%;
    --primary-dark: 210 75% 45%;

    --secondary: 220 15% 18%;
    --secondary-foreground: 210 25% 90%;

    --muted: 220 15% 15%;
    --muted-foreground: 220 10% 65%;

    --accent: 195 85% 65%;
    --accent-foreground: 220 25% 8%;

    --destructive: 0 75% 65%;
    --destructive-foreground: 220 25% 8%;

    --border: 220 15% 20%;
    --input: 220 15% 18%;
    --ring: 210 85% 65%;

    /* Dark Healthcare Gradients */
    --gradient-primary: linear-gradient(135deg, hsl(210 85% 65%), hsl(195 85% 65%));
    --gradient-card: linear-gradient(145deg, hsl(220 20% 12%), hsl(220 25% 8%));
    --gradient-hero: linear-gradient(135deg, hsl(210 85% 65%) 0%, hsl(195 85% 65%) 50%, hsl(210 75% 45%) 100%);
    
    /* Dark Professional Shadows */
    --shadow-sm: 0 2px 4px hsla(220, 25%, 2%, 0.3);
    --shadow-md: 0 4px 12px hsla(220, 25%, 2%, 0.4);
    --shadow-lg: 0 8px 24px hsla(220, 25%, 2%, 0.5);
    --shadow-card: 0 2px 8px hsla(220, 30%, 2%, 0.4);

    /* Dark Healthcare Status Colors */
    --success: 145 65% 60%;
    --success-foreground: 220 25% 8%;
    --warning: 45 90% 70%;
    --warning-foreground: 45 15% 15%;
    --info: 210 85% 65%;
    --info-foreground: 220 25% 8%;

    --sidebar-background: 220 25% 8%;
    --sidebar-foreground: 210 25% 90%;
    --sidebar-primary: 210 85% 65%;
    --sidebar-primary-foreground: 220 25% 8%;
    --sidebar-accent: 220 15% 15%;
    --sidebar-accent-foreground: 210 25% 90%;
    --sidebar-border: 220 15% 20%;
    --sidebar-ring: 210 85% 65%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}

/* RTL Support */
[dir="rtl"] {
  direction: rtl;
}

[dir="rtl"] .flex {
  flex-direction: row-reverse;
}

[dir="rtl"] .gap-2 > *:not(:last-child),
[dir="rtl"] .gap-3 > *:not(:last-child),
[dir="rtl"] .gap-4 > *:not(:last-child) {
  margin-left: 0;
  margin-right: var(--gap-size, 0.5rem);
}

/* Language switcher specific styles */
.language-switcher {
  transition: all 0.2s ease-in-out;
}

.language-switcher:hover {
  transform: translateY(-1px);
}
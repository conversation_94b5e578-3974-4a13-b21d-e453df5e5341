import { useTranslation } from 'react-i18next';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

export function LocalizationTest() {
  const { t, i18n } = useTranslation();

  return (
    <Card className="w-full max-w-md mx-auto mt-4">
      <CardHeader>
        <CardTitle>{t('dashboard.welcome')}</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-2">
          <p><strong>{t('language.english')}:</strong> {t('header.title')}</p>
          <p><strong>{t('language.arabic')}:</strong> نماء من أستر</p>
          <p><strong>{t('common.loading')}:</strong> {t('common.loading')}</p>
          <p><strong>Current Language:</strong> {i18n.language}</p>
          <p><strong>Direction:</strong> {document.documentElement.dir}</p>
        </div>
      </CardContent>
    </Card>
  );
}

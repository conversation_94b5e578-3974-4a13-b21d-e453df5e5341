import { useState } from "react";
import { NavLink } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { useLanguage } from "@/hooks/useLanguage";
import {
  BookOpen,
  Calendar,
  Award,
  GraduationCap,
  BarChart3,
  Home,
  Clock,
  User,
  LogOut,
  Bell,
  Settings
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import logo from "@/assets/logo.png";
import { LanguageSwitcher } from "@/components/LanguageSwitcher";

interface LayoutProps {
  children: React.ReactNode;
  onLogout: () => void;
}

const navigationItems = [
  { titleKey: "navigation.dashboard", url: "/dashboard", icon: Home },
  { titleKey: "navigation.nominatedTrainings", url: "/nominated-trainings", icon: BookOpen },
  { titleKey: "navigation.upcomingTrainings", url: "/upcoming-trainings", icon: Clock },
  { titleKey: "navigation.activeCertifications", url: "/active-certifications", icon: Award },
  { titleKey: "navigation.learningCalendar", url: "/learning-calendar", icon: Calendar },
  { titleKey: "navigation.learningSummary", url: "/learning-summary", icon: GraduationCap },
  { titleKey: "navigation.analytics", url: "/analytics", icon: BarChart3 },
];

export function Layout({ children, onLogout }: LayoutProps) {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const { t } = useTranslation();
  const { isRTL } = useLanguage();

  const toggleSidebar = () => {
    setIsMenuOpen(!isMenuOpen);
  };

  const getNavClassName = ({ isActive }: { isActive: boolean }) => {
    return cn(
      "flex items-center text-sm font-medium rounded-lg transition-all duration-200 min-w-0",
      "hover:bg-primary/10 hover:text-primary hover:shadow-sm",
      isMenuOpen
        ? "gap-3 px-4 py-3 w-full"
        : "justify-center p-3 mx-2 w-auto",
      isActive
        ? "bg-gradient-to-r from-primary to-primary/80 text-primary-foreground shadow-md border border-primary/20"
        : "text-muted-foreground hover:text-foreground"
    );
  };

  return (
    <div className="h-screen flex flex-col w-full overflow-hidden">
      {/* Fixed Header */}
      <header className="h-16 bg-card/50 backdrop-blur supports-[backdrop-filter]:bg-card/50 border-b border-border shadow-sm flex-shrink-0 z-50">
        <div className="flex h-full items-center justify-between px-6">
          <div className="flex items-center gap-4">
            <div className="flex items-center gap-3">
              <img
                src={logo}
                alt="Namaa by Aster Logo"
                className="w-16 h-16 object-contain"
              />
              <div>
                <h1 className="text-xl font-semibold text-foreground">{t('header.title')}</h1>
                <p className="text-xs text-muted-foreground">{t('header.subtitle')}</p>
              </div>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <LanguageSwitcher />
            <Button variant="ghost" size="sm" title={t('header.notifications')}>
              <Bell className="w-4 h-4" />
            </Button>
            <Button variant="ghost" size="sm" title={t('header.settings')}>
              <Settings className="w-4 h-4" />
            </Button>
          </div>
        </div>
      </header>

      {/* Content Area with Fixed Sidebar */}
      <div className="flex flex-1 overflow-hidden">
        {/* Fixed Sidebar */}
        <div
          className={cn(
            "bg-card border-r border-border shadow-lg transition-all duration-300 ease-in-out flex-shrink-0 z-40 overflow-hidden h-full cursor-pointer",
            isMenuOpen ? "w-80 hover:bg-muted/10" : "w-16 hover:bg-muted/30"
          )}
          onClick={(e) => {
            e.stopPropagation();
            toggleSidebar();
          }}
          title={isMenuOpen ? "Click to collapse sidebar" : "Click to expand sidebar"}
        >
          <div className="flex flex-col h-full min-w-0">



            {/* Navigation Menu - Scrollable Content */}
            <div className={cn(
              "flex-1 overflow-y-auto overflow-x-hidden transition-all duration-300 min-h-0",
              isMenuOpen ? "p-4" : "py-4 px-1"
            )}>
              <div className="space-y-6">
                {/* Main Navigation */}
                <div>
                  {isMenuOpen && (
                    <h3 className="text-sm font-semibold text-muted-foreground uppercase tracking-wider mb-3 px-3">
                      Navigation
                    </h3>
                  )}
                  <div className={cn(
                    "transition-all duration-300",
                    isMenuOpen ? "space-y-1" : "space-y-2"
                  )}>
                    {navigationItems.map((item) => (
                      <NavLink
                        key={item.titleKey}
                        to={item.url}
                        className={getNavClassName}
                        title={!isMenuOpen ? t(item.titleKey) : undefined}
                        onClick={(e) => e.stopPropagation()}
                      >
                        <item.icon className="w-5 h-5 flex-shrink-0" />
                        {isMenuOpen && <span>{t(item.titleKey)}</span>}
                      </NavLink>
                    ))}
                  </div>
                </div>
              </div>
            </div>

            {/* Fixed Sidebar Footer */}
            <div className={cn(
              "border-t border-border bg-muted/30 transition-all duration-300 overflow-hidden flex-shrink-0",
              isMenuOpen ? "p-4" : "py-4 px-1"
            )}>
              <div className={cn(
                "transition-all duration-300 min-w-0",
                isMenuOpen ? "space-y-2" : "space-y-3"
              )}>
                <NavLink
                  to="/profile"
                  className={getNavClassName}
                  title={!isMenuOpen ? t('header.profile') : undefined}
                  onClick={(e) => e.stopPropagation()}
                >
                  <User className="w-5 h-5 flex-shrink-0" />
                  {isMenuOpen && <span>{t('header.profile')}</span>}
                </NavLink>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={(e) => {
                    e.stopPropagation();
                    onLogout();
                  }}
                  className={cn(
                    "text-destructive hover:text-destructive hover:bg-destructive/10 transition-all duration-200 min-w-0",
                    isMenuOpen
                      ? "justify-start px-4 py-3 w-full"
                      : "justify-center p-3 mx-2 w-auto"
                  )}
                  title={!isMenuOpen ? t('header.logout') : undefined}
                >
                  <LogOut className="w-5 h-5 flex-shrink-0" />
                  {isMenuOpen && <span className="ml-3">{t('header.logout')}</span>}
                </Button>
              </div>
            </div>
          </div>
        </div>

        {/* Main Content - Scrollable */}
        <main className="flex-1 overflow-auto min-w-0 h-full">
          {children}
        </main>
      </div>


    </div>
  );
}